import { Chat, Chat<PERSON>ess<PERSON>, ChatThread, GuestSession } from '../models/chat/index.js';
import { LLMService } from './LLMService.js';
import { CreditService } from './CreditService.js';
import { S3Service } from './S3Service.js';
import { FileProcessingService } from './FileProcessingService.js';
import { EncryptionUtil } from '../utils/encryption.js';
import { VALIDATION_RULES, ERROR_MESSAGES, CREDIT_SYSTEM } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * @typedef {Object} ChatRequest
 * @property {string} message - User message
 * @property {string} [sessionId] - Session ID
 * @property {string} [llmModel] - LLM model to use
 */

/**
 * @typedef {Object} ChatResponse
 * @property {string} response - AI response
 * @property {string} sessionId - Session ID
 * @property {string} messageId - Message ID
 */

/**
 * @typedef {Object} SimpleChatRequest
 * @property {string} message - User message
 * @property {string} [sessionId] - Session ID
 * @property {string} [llmModel] - LLM model to use
 */

/**
 * @typedef {Object} SimpleChatResponse
 * @property {string} response - AI response
 * @property {string} sessionId - Session ID
 * @property {string} messageId - Message ID
 */

/**
 * @typedef {Object} RegenerateRequest
 * @property {string} messageId - Message ID to regenerate
 */

export class ChatService {
  /**
   * Process chat message for authenticated user
   * @param {string} userId - User ID
   * @param {ChatRequest} chatRequest - Chat request data
   * @returns {Promise<ChatResponse>} Chat response
   */
  static async processUserChat(userId, chatRequest) {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits (1 credit per message)
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let chat;
      if (sessionId) {
        // Find existing chat by session ID
        chat = await Chat.findBySessionId(sessionId, userId);
        if (!chat) {
          throw new Error('Chat session not found');
        }
      } else {
        // Create new chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();
        chat = await Chat.createChat({
          userId,
          sessionId: newSessionId,
          isGuest: false,
        });
        logger.info(`Created new chat for user with generated session ID: ${newSessionId}`);
      }

      // Generate response using LLM
      const conversationHistory = await this.getChatHistory(chat.id);
      const response = await LLMService.generateResponse(
        message,
        llmModel,
        this.getSystemPrompt(),
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: chat.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
      });

      // Deduct credit
      const creditDeducted = await CreditService.deductCredits(
        userId,
        CREDIT_SYSTEM.CHAT_MESSAGE_COST,
        CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
      );
      
      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing message`);
      }

      logger.info(`Processed chat message for user ${userId}, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: chatMessage.id,
      };
    } catch (error) {
      logger.error('Error processing user chat:', error);
      throw error;
    }
  }

  /**
   * Process chat message for guest user
   * @param {string} guestId - Guest ID
   * @param {ChatRequest} chatRequest - Chat request data
   * @returns {Promise<ChatResponse>} Chat response
   */
  static async processGuestChat(guestId, chatRequest) {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      let chat;
      if (sessionId) {
        // Find existing guest chat by session ID
        chat = await Chat.findGuestBySessionId(sessionId, guestId);
        if (!chat) {
          throw new Error('Guest chat session not found');
        }
      } else {
        // Create new guest chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();
        
        // Create or find guest session
        const [guestSession] = await GuestSession.findOrCreateSession(newSessionId, '127.0.0.1');

        chat = await Chat.createChat({
          guestSessionId: guestSession.id,
          sessionId: newSessionId,
          isGuest: true,
        });
        logger.info(`Created new guest chat with session ID: ${newSessionId}`);
      }

      // Generate response using LLM
      const conversationHistory = await this.getChatHistory(chat.id);
      const response = await LLMService.generateResponse(
        message,
        llmModel,
        this.getSystemPrompt(),
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: chat.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
      });

      logger.info(`Processed guest chat message for guest ${guestId}, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: chatMessage.id,
      };
    } catch (error) {
      logger.error('Error processing guest chat:', error);
      throw error;
    }
  }

  /**
   * Simplified chat for authenticated users
   * @param {string} userId - User ID
   * @param {SimpleChatRequest} chatRequest - Chat request data
   * @returns {Promise<SimpleChatResponse>} Chat response
   */
  static async processSimpleUserChat(userId, chatRequest) {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits (1 credit per message)
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let chat;
      if (sessionId) {
        // Find existing chat by session ID
        chat = await Chat.findBySessionId(sessionId, userId);
        if (!chat) {
          // Create new chat with provided session ID
          chat = await Chat.createChat({
            userId,
            sessionId,
            isGuest: false,
          });
          logger.info(`Created new chat for user with provided session ID: ${sessionId}`);
        }
      } else {
        // Create new chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();
        chat = await Chat.createChat({
          userId,
          sessionId: newSessionId,
          isGuest: false,
        });
        logger.info(`Created new chat for user with generated session ID: ${newSessionId}`);
      }

      // Generate response using LLM
      const conversationHistory = await this.getChatHistory(chat.id);
      const response = await LLMService.generateResponse(
        message,
        llmModel,
        this.getSystemPrompt(),
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: chat.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
      });

      // Deduct credit
      const creditDeducted = await CreditService.deductCredits(
        userId,
        CREDIT_SYSTEM.CHAT_MESSAGE_COST,
        CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
      );
      
      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing message`);
      }

      logger.info(`Processed simple chat message for user ${userId}, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: chatMessage.id,
      };
    } catch (error) {
      logger.error('Error processing simple user chat:', error);
      throw error;
    }
  }

  /**
   * Simplified chat for guest users
   * @param {string} guestId - Guest ID
   * @param {SimpleChatRequest} chatRequest - Chat request data
   * @returns {Promise<SimpleChatResponse>} Chat response
   */
  static async processSimpleGuestChat(guestId, chatRequest) {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      let chat;
      if (sessionId) {
        // Find existing guest chat by session ID
        chat = await Chat.findGuestBySessionId(sessionId, guestId);
        if (!chat) {
          // Create new guest chat with provided session ID
          const [guestSession] = await GuestSession.findOrCreateSession(sessionId, '127.0.0.1');

          chat = await Chat.createChat({
            guestSessionId: guestSession.id,
            sessionId,
            isGuest: true,
          });
          logger.info(`Created new guest chat with provided session ID: ${sessionId}`);
        }
      } else {
        // Create new guest chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();
        
        // Create or find guest session
        const [guestSession] = await GuestSession.findOrCreateSession(newSessionId, '127.0.0.1');

        chat = await Chat.createChat({
          guestSessionId: guestSession.id,
          sessionId: newSessionId,
          isGuest: true,
        });
        logger.info(`Created new guest chat with session ID: ${newSessionId}`);
      }

      // Generate response using LLM
      const conversationHistory = await this.getChatHistory(chat.id);
      const response = await LLMService.generateResponse(
        message,
        llmModel,
        this.getSystemPrompt(),
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: chat.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
      });

      logger.info(`Processed simple guest chat message for guest ${guestId}, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: chatMessage.id,
      };
    } catch (error) {
      logger.error('Error processing simple guest chat:', error);
      throw error;
    }
  }

  /**
   * Streaming chat for authenticated users
   * @param {string} userId - User ID
   * @param {SimpleChatRequest} chatRequest - Chat request data
   * @param {Object} res - Express response object
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Promise<SimpleChatResponse>} Chat response
   */
  static async processSimpleUserChatStreaming(userId, chatRequest, res, attachedFile) {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      // Allow empty message if file is attached
      if ((!message || message.trim().length === 0) && !attachedFile) {
        throw new Error('Message cannot be empty when no file is attached');
      }

      // Check if user has enough credits (1 credit per message)
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let chat;
      if (sessionId) {
        // Find existing chat by session ID
        chat = await ChatThread.findBySessionId(sessionId, userId);
        if (!chat) {
          const initialName = ChatThread.generateThreadName(message);
          // Create new chat with provided session ID
          chat = await ChatThread.createThread({
            userId,
            sessionId,
            isGuest: false,
            name: initialName,
          });
          logger.info(`Created new chat for user with provided session ID: ${sessionId}`);
        }
      } else {
        const initialName = ChatThread.generateThreadName(message);
        // Create new chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();
        chat = await ChatThread.createThread({
          userId,
          sessionId: newSessionId,
          isGuest: false,
          name: initialName,
        });
        logger.info(`Created new chat for user with generated session ID: ${newSessionId}`);
      }

      // Generate response using LLM with streaming
      const conversationHistory = await this.getChatHistory(chat.id);
      const metadata = {
        sessionId: chat.sessionId,
        userId,
        chatId: chat.id,
      };

      const response = await LLMService.generateStreamingResponse(
          message || '',
          res,
          llmModel,
          this.getSystemPrompt(),
          conversationHistory,
          metadata,
          async (fullResponse) => {
            // Save message and response with attachment metadata
            const messageData = {
              chatId: chat.id,
              message: message || '',
              response: fullResponse,
              llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
            };

            // Add attachment metadata if file was attached
            if (attachedFile && attachedFile.metadata) {
              messageData.attachmentPath = attachedFile.filePath;
              messageData.attachmentName = attachedFile.metadata.originalName;
              messageData.attachmentType = attachedFile.metadata.mimeType;
              messageData.attachmentSize = attachedFile.metadata.size;
              messageData.attachmentS3Url = attachedFile.metadata.s3Url;
              messageData.attachmentS3Key = attachedFile.metadata.s3Key;
              messageData.attachmentStorageType = attachedFile.metadata.storageType;
              messageData.attachmentSecureId = attachedFile.metadata.secureFileId;
            }

            const chatMessage = await ChatMessage.createMessage(messageData);

            // Deduct credit
            const creditDeducted = await CreditService.deductCredits(
                userId,
                CREDIT_SYSTEM.CHAT_MESSAGE_COST,
                CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
            );

            if (!creditDeducted) {
              logger.error(`Failed to deduct credit for user ${userId} after processing message`);
            }

            // Return updated metadata with messageId
            return {
              messageId: chatMessage.id
            };
          },
          attachedFile
      );

      logger.info(`Processed streaming chat message for user ${userId}, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: 'will-be-set-in-callback',
      };
    } catch (error) {
      logger.error('Error processing streaming user chat:', error);
      throw error;
    }
  }

  /**
   * Streaming chat for guest users
   * @param {string} guestId - Guest ID
   * @param {SimpleChatRequest} chatRequest - Chat request data
   * @param {Object} res - Express response object
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Promise<SimpleChatResponse>} Chat response
   */
  static async processSimpleGuestChatStreaming(guestId, chatRequest, res, attachedFile) {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      // Allow empty message if file is attached
      if ((!message || message.trim().length === 0) && !attachedFile) {
        throw new Error('Message cannot be empty when no file is attached');
      }

      let chat;
      if (sessionId) {
        // Find existing guest chat by session ID
        chat = await ChatThread.findGuestBySessionId(sessionId, guestId);
        if (!chat) {
          // Create new guest chat with provided session ID
          const [guestSession] = await GuestSession.findOrCreateSession(sessionId, '127.0.0.1');
          const initialName = ChatThread.generateThreadName(message);
          chat = await ChatThread.createThread({
            guestSessionId: guestSession.id,
            sessionId,
            isGuest: true,
            name: initialName,
          });
          logger.info(`Created new guest chat with provided session ID: ${sessionId}`);
        }
      } else {
        // Create new guest chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();

        // Create or find guest session
        const [guestSession] = await GuestSession.findOrCreateSession(newSessionId, '127.0.0.1');

        const initialName = ChatThread.generateThreadName(message);
        chat = await ChatThread.createThread({
          guestSessionId: guestSession.id,
          sessionId: newSessionId,
          isGuest: true,
          name: initialName,
        });
        logger.info(`Created new guest chat with session ID: ${newSessionId}`);
      }

      // Generate response using LLM with streaming
      const conversationHistory = await this.getChatHistory(chat.id);
      const metadata = {
        sessionId: chat.sessionId,
        guestId,
        chatId: chat.id,
      };

      const response = await LLMService.generateStreamingResponse(
          message || '',
          res,
          llmModel,
          this.getSystemPrompt(),
          conversationHistory,
          metadata,
          async (fullResponse) => {
            // Save message and response
            const chatMessage = await ChatMessage.createMessage({
              chatId: chat.id,
              message: message || '',
              response: fullResponse,
              llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
            });

            // Return updated metadata with messageId
            return {
              messageId: chatMessage.id
            };
          },
          attachedFile
      );

      logger.info(`Processed streaming guest chat message for guest ${guestId}, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: 'will-be-set-in-callback',
      };
    } catch (error) {
      logger.error('Error processing streaming guest chat:', error);
      throw error;
    }
  }

  /**
   * Regenerate chat message response
   * @param {string} userId - User ID
   * @param {RegenerateRequest} regenerateRequest - Regenerate request data
   * @returns {Promise<Object>} Regenerated response
   */
  static async regenerateMessage(userId, regenerateRequest) {
    try {
      const { messageId } = regenerateRequest;

      if (!messageId) {
        throw new Error('Message ID is required');
      }

      // Find the message
      const chatMessage = await ChatMessage.findByPk(messageId, {
        include: [{
          model: Chat,
          as: 'chat',
          where: { userId },
        }]
      });

      if (!chatMessage) {
        throw new Error('Message not found');
      }

      // Check if user has enough credits
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      // Get conversation history up to this message
      const conversationHistory = await this.getChatHistoryBeforeMessage(chatMessage.chatId, messageId);

      // Generate new response
      const newResponse = await LLMService.generateResponse(
          chatMessage.message,
          chatMessage.llmModel,
          this.getSystemPrompt(),
          conversationHistory
      );

      // Update the message with new response
      await chatMessage.update({ response: newResponse });

      // Deduct credit
      const creditDeducted = await CreditService.deductCredits(
          userId,
          CREDIT_SYSTEM.CHAT_MESSAGE_COST,
          'Message regeneration'
      );

      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after regenerating message`);
      }

      logger.info(`Regenerated message ${messageId} for user ${userId}`);

      return {
        messageId: chatMessage.id,
        response: newResponse,
      };
    } catch (error) {
      logger.error('Error regenerating message:', error);
      throw error;
    }
  }

  /**
   * Get chat history for a chat
   * @param {string} chatId - Chat ID
   * @param {number} [limit] - Limit number of messages
   * @returns {Promise<Array>} Chat history
   */
  static async getChatHistory(chatId, limit = 10) {
    try {
      const messages = await ChatMessage.findAll({
        where: { chatId },
        order: [['createdAt', 'ASC']],
        limit,
      });

      return messages.map(msg => [
        { role: 'user', content: msg.message },
        { role: 'assistant', content: msg.response }
      ]).flat();
    } catch (error) {
      logger.error('Error getting chat history:', error);
      return [];
    }
  }

  /**
   * Get chat history before a specific message
   * @param {string} chatId - Chat ID
   * @param {string} messageId - Message ID
   * @returns {Promise<Array>} Chat history before message
   */
  static async getChatHistoryBeforeMessage(chatId, messageId) {
    try {
      const { Op } = await import('sequelize');

      const messages = await ChatMessage.findAll({
        where: {
          chatId,
          id: {
            [Op.ne]: messageId
          }
        },
        order: [['createdAt', 'ASC']],
      });

      return messages.map(msg => [
        { role: 'user', content: msg.message },
        { role: 'assistant', content: msg.response }
      ]).flat();
    } catch (error) {
      logger.error('Error getting chat history before message:', error);
      return [];
    }
  }

  /**
   * Get system prompt for chat
   * @returns {string} System prompt
   */
  static getSystemPrompt() {
    return 'You are a helpful assistant. Provide accurate, helpful, and concise responses to user questions. Use proper delimiters for Latex Expressions($...$ for inline math and $$...$$ for block math).';
  }

  /**
   * Get user chats
   * @param {string} userId - User ID
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} User chats
   */
  static async getUserChats(userId, limit = 20, offset = 0) {
    try {
      return await Chat.findAll({
        where: { userId },
        order: [['updatedAt', 'DESC']],
        limit,
        offset,
      });
    } catch (error) {
      logger.error('Error getting user chats:', error);
      throw error;
    }
  }

  /**
   * Delete chat and all messages
   * @param {string} chatId - Chat ID
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async deleteChat(chatId, userId) {
    try {
      const chat = await Chat.findOne({
        where: { id: chatId, userId }
      });

      if (!chat) {
        throw new Error('Chat not found');
      }

      // Delete all messages
      await ChatMessage.destroy({
        where: { chatId }
      });

      // Delete chat
      await chat.destroy();

      logger.info(`Deleted chat ${chatId} for user ${userId}`);
    } catch (error) {
      logger.error('Error deleting chat:', error);
      throw error;
    }
  }

  /**
   * Streaming chat for authenticated users with file attachment support
   * @param {string} userId - User ID
   * @param {SimpleChatRequest} chatRequest - Chat request data
   * @param {Object} res - Express response object
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Promise<SimpleChatResponse>} Chat response
   */
  static async processSimpleUserChatStreamingWithAttachment(userId, chatRequest, res, attachedFile) {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      // Allow empty message if file is attached
      if ((!message || message.trim().length === 0) && !attachedFile) {
        throw new Error('Message cannot be empty when no file is attached');
      }

      // Check if user has enough credits (1 credit per message)
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let chat;
      if (sessionId) {
        // Find existing chat by session ID
        chat = await ChatThread.findBySessionId(sessionId, userId);
        if (!chat) {
          const initialName = ChatThread.generateThreadName(message);
          // Create new chat with provided session ID
          chat = await ChatThread.createThread({
            userId,
            sessionId,
            isGuest: false,
            name: initialName,
          });
          logger.info(`Created new chat for user with provided session ID: ${sessionId}`);
        }
      } else {
        const initialName = ChatThread.generateThreadName(message);
        // Create new chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();
        chat = await ChatThread.createThread({
          userId,
          sessionId: newSessionId,
          isGuest: false,
          name: initialName,
        });
        logger.info(`Created new chat for user with generated session ID: ${newSessionId}`);
      }

      // Generate response using LLM with streaming
      const conversationHistory = await this.getChatHistory(chat.id);
      const metadata = {
        sessionId: chat.sessionId,
        userId,
        chatId: chat.id,
      };

      const response = await LLMService.generateStreamingResponse(
          message || '',
          res,
          llmModel,
          this.getSystemPrompt(),
          conversationHistory,
          metadata,
          async (fullResponse) => {
            // Save message and response with attachment metadata
            const messageData = {
              chatId: chat.id,
              message: message || '',
              response: fullResponse,
              llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
            };

            // Add attachment metadata if file was attached
            if (attachedFile && attachedFile.metadata) {
              messageData.attachmentPath = attachedFile.filePath;
              messageData.attachmentName = attachedFile.metadata.originalName;
              messageData.attachmentType = attachedFile.metadata.mimeType;
              messageData.attachmentSize = attachedFile.metadata.size;
              messageData.attachmentS3Url = attachedFile.metadata.s3Url;
              messageData.attachmentS3Key = attachedFile.metadata.s3Key;
              messageData.attachmentStorageType = attachedFile.metadata.storageType;
              messageData.attachmentSecureId = attachedFile.metadata.secureFileId;
            }

            const chatMessage = await ChatMessage.createMessage(messageData);

            // Deduct credit
            const creditDeducted = await CreditService.deductCredits(
                userId,
                CREDIT_SYSTEM.CHAT_MESSAGE_COST,
                CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
            );

            if (!creditDeducted) {
              logger.error(`Failed to deduct credit for user ${userId} after processing message`);
            }

            // Return updated metadata with messageId
            return {
              messageId: chatMessage.id
            };
          },
          attachedFile
      );

      logger.info(`Processed streaming chat with attachment for user ${userId}, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: 'will-be-set-in-callback',
      };
    } catch (error) {
      logger.error('Error processing streaming user chat with attachment:', error);
      throw error;
    }
  }

  /**
   * Streaming chat for guest users with file attachment support
   * @param {SimpleChatRequest} chatRequest - Chat request data
   * @param {Object} res - Express response object
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Promise<SimpleChatResponse>} Chat response
   */
  static async processSimpleGuestChatStreamingWithAttachment(chatRequest, res, attachedFile) {
    try {
      const { message, sessionId, llmModel } = chatRequest;

      // Allow empty message if file is attached
      if ((!message || message.trim().length === 0) && !attachedFile) {
        throw new Error('Message cannot be empty when no file is attached');
      }

      // Generate a guest ID if not provided
      const guestId = EncryptionUtil.generateSessionId();

      let chat;
      if (sessionId) {
        // Find existing guest chat by session ID
        chat = await Chat.findGuestBySessionId(sessionId, guestId);
        if (!chat) {
          // Create new guest chat with provided session ID
          const [guestSession] = await GuestSession.findOrCreateSession(sessionId, '127.0.0.1');

          chat = await Chat.createChat({
            guestSessionId: guestSession.id,
            sessionId,
            isGuest: true,
          });
          logger.info(`Created new guest chat with provided session ID: ${sessionId}`);
        }
      } else {
        // Create new guest chat with generated session ID
        const newSessionId = EncryptionUtil.generateSessionId();

        // Create or find guest session
        const [guestSession] = await GuestSession.findOrCreateSession(newSessionId, '127.0.0.1');

        chat = await Chat.createChat({
          guestSessionId: guestSession.id,
          sessionId: newSessionId,
          isGuest: true,
        });
        logger.info(`Created new guest chat with session ID: ${newSessionId}`);
      }

      // Generate response using LLM with streaming
      const conversationHistory = await this.getChatHistory(chat.id);
      const metadata = {
        sessionId: chat.sessionId,
        guestId,
        chatId: chat.id,
      };

      const response = await LLMService.generateStreamingResponse(
          message || '',
          res,
          llmModel,
          this.getSystemPrompt(),
          conversationHistory,
          metadata,
          async (fullResponse) => {
            // Save message and response with attachment metadata
            const messageData = {
              chatId: chat.id,
              message: message || '',
              response: fullResponse,
              llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
            };

            // Add attachment metadata if file was attached
            if (attachedFile && attachedFile.metadata) {
              messageData.attachmentPath = attachedFile.filePath;
              messageData.attachmentName = attachedFile.metadata.originalName;
              messageData.attachmentType = attachedFile.metadata.mimeType;
              messageData.attachmentSize = attachedFile.metadata.size;
              messageData.attachmentS3Url = attachedFile.metadata.s3Url;
              messageData.attachmentS3Key = attachedFile.metadata.s3Key;
              messageData.attachmentStorageType = attachedFile.metadata.storageType;
              messageData.attachmentSecureId = attachedFile.metadata.secureFileId;
            }

            const chatMessage = await ChatMessage.createMessage(messageData);

            // Return updated metadata with messageId
            return {
              messageId: chatMessage.id
            };
          },
          attachedFile
      );

      logger.info(`Processed streaming guest chat with attachment, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: 'will-be-set-in-callback',
      };
    } catch (error) {
      logger.error('Error processing streaming guest chat with attachment:', error);
      throw error;
    }
  }

  /**
   * Regenerate AI response for a specific message with streaming
   * @param {string} messageId - Message ID to regenerate
   * @param {string} [userId] - User ID (optional for guest users)
   * @param {string} [llmModel] - LLM model to use
   * @param {Object} res - Express response object
   * @param {string} [attachmentUrl] - Optional attachment URL in format /files/{fileId}
   * @returns {Promise<Object>} Regeneration result
   */
  static async regenerateResponse(messageId, userId, llmModel, res, attachmentUrl) {
    try {
      if (!messageId) {
        throw new Error('Message ID is required');
      }

      // Find the message
      let chatMessage;
      if (userId) {
        // For authenticated users
        chatMessage = await ChatMessage.findByPk(messageId, {
          include: [{
            model: ChatThread,
            as: 'thread',
            where: { userId, isGuest: false },
          }]
        });
      } else {
        // For guest users
        chatMessage = await ChatMessage.findByPk(messageId, {
          include: [{
            model: ChatThread,
            as: 'thread',
            where: { isGuest: true },
          }]
        });
      }

      if (!chatMessage) {
        throw new Error('Message not found');
      }

      // Check if user has enough credits (only for authenticated users)
      if (userId) {
        const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
        if (!hasCredits) {
          throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
        }
      }

      // Get conversation history up to this message
      const conversationHistory = await this.getChatHistoryBeforeMessage(chatMessage.chatId, messageId);

      // Process attachment if provided
      let processedFile = null;
      if (attachmentUrl) {
        processedFile = await this.processAttachmentFromUrl(attachmentUrl);
      }

      // Prepare metadata for streaming
      const metadata = {
        messageId: chatMessage.id,
        chatId: chatMessage.chatId,
        sessionId: chatMessage.thread.sessionId,
        userId: userId || null,
        isRegeneration: true,
        hasAttachment: !!processedFile,
      };

      // Generate new response with streaming
      const response = await LLMService.generateStreamingResponse(
        chatMessage.message,
        res,
        llmModel || chatMessage.llmModel,
        this.getSystemPrompt(),
        conversationHistory,
        metadata,
        async (fullResponse) => {
          // Update the message with new response
          await chatMessage.update({
            response: fullResponse,
            llmModel: llmModel || chatMessage.llmModel
          });

          // Deduct credit (only for authenticated users)
          if (userId) {
            const creditDeducted = await CreditService.deductCredits(
              userId,
              CREDIT_SYSTEM.CHAT_MESSAGE_COST,
              'Message regeneration'
            );

            if (!creditDeducted) {
              logger.error(`Failed to deduct credit for user ${userId} after regenerating message`);
            }
          }

          return {
            messageId: chatMessage.id,
            regenerated: true
          };
        },
        processedFile
      );

      logger.info(`Regenerated message ${messageId} ${userId ? `for user ${userId}` : 'for guest'}`);

      return {
        messageId: chatMessage.id,
        response: 'will-be-streamed',
        regenerated: true,
      };
    } catch (error) {
      logger.error('Error regenerating message:', error);
      throw error;
    }
  }

  /**
   * Delete user chat
   * @param {string} chatId - Chat ID
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async deleteUserChat(chatId, userId) {
    return this.deleteChat(chatId, userId);
  }

  /**
   * Delete guest chat
   * @param {string} chatId - Chat ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<void>}
   */
  static async deleteGuestChat(chatId, sessionId) {
    try {
      const chat = await Chat.findOne({
        where: { id: chatId, sessionId, isGuest: true }
      });

      if (!chat) {
        throw new Error('Guest chat not found');
      }

      // Delete all messages
      await ChatMessage.destroy({
        where: { chatId }
      });

      // Delete chat
      await chat.destroy();

      logger.info(`Deleted guest chat ${chatId} with session ${sessionId}`);
    } catch (error) {
      logger.error('Error deleting guest chat:', error);
      throw error;
    }
  }

  /**
   * Update chat title
   * @param {string} chatId - Chat ID
   * @param {string} userId - User ID
   * @param {string} title - New title
   * @returns {Promise<void>}
   */
  static async updateChatTitle(chatId, userId, title) {
    try {
      const chat = await ChatThread.findOne({
        where: { id: chatId, userId }
      });

      if (!chat) {
        throw new Error('Chat not found');
      }

      await chat.update({ title });
      logger.info(`Updated chat title for ${chatId}`);
    } catch (error) {
      logger.error('Error updating chat title:', error);
      throw error;
    }
  }

  /**
   * Update guest chat title
   * @param {string} chatId - Chat ID
   * @param {string} sessionId - Session ID
   * @param {string} title - New title
   * @returns {Promise<void>}
   */
  static async updateGuestChatTitle(chatId, sessionId, title) {
    try {
      const chat = await ChatThread.findOne({
        where: { id: chatId, sessionId, isGuest: true }
      });

      if (!chat) {
        throw new Error('Guest chat not found');
      }

      await chat.update({ title });
      logger.info(`Updated guest chat title for ${chatId}`);
    } catch (error) {
      logger.error('Error updating guest chat title:', error);
      throw error;
    }
  }

  /**
   * Convert guest chat to user chat
   * @param {string} sessionId - Session ID
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async convertGuestChatToUser(sessionId, userId) {
    try {
      const guestChats = await ChatThread.findAll({
        where: { sessionId, isGuest: true }
      });

      for (const chat of guestChats) {
        await chat.update({
          userId,
          isGuest: false,
          guestSessionId: null
        });
      }

      logger.info(`Converted ${guestChats.length} guest chats to user chats for user ${userId}`);
    } catch (error) {
      logger.error('Error converting guest chat to user:', error);
      throw error;
    }
  }

  /**
   * Get chat messages
   * @param {string} chatId - Chat ID
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Chat messages
   */
  static async getChatMessages(chatId, userId) {
    try {
      const chat = await ChatThread.findOne({
        where: { id: chatId, userId }
      });

      if (!chat) {
        throw new Error('Chat not found');
      }

      const messages = await ChatMessage.findAll({
        where: { chatId },
        order: [['createdAt', 'ASC']]
      });

      // Transform messages for secure output
      return messages.map(message => ChatMessage.transformForSecureOutput(message));
    } catch (error) {
      logger.error('Error getting chat messages:', error);
      throw error;
    }
  }

  /**
   * Get guest chat messages
   * @param {string} chatId - Chat ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<Array>} Chat messages
   */
  static async getGuestChatMessages(chatId, sessionId) {
    try {
      const chat = await ChatThread.findOne({
        where: { id: chatId, sessionId, isGuest: true }
      });

      if (!chat) {
        throw new Error('Guest chat not found');
      }

      const messages = await ChatMessage.findAll({
        where: { chatId },
        order: [['createdAt', 'ASC']]
      });

      // Transform messages for secure output
      return messages.map(message => ChatMessage.transformForSecureOutput(message));
    } catch (error) {
      logger.error('Error getting guest chat messages:', error);
      throw error;
    }
  }

  /**
   * Get guest session info
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object>} Session info
   */
  static async getGuestSessionInfo(sessionId) {
    try {
      const chats = await ChatThread.findAll({
        where: { sessionId, isGuest: true },
        order: [['updatedAt', 'DESC']]
      });

      return {
        sessionId,
        chatCount: chats.length,
        chats: chats.map(chat => ({
          id: chat.id,
          title: chat.title,
          createdAt: chat.createdAt,
          updatedAt: chat.updatedAt
        }))
      };
    } catch (error) {
      logger.error('Error getting guest session info:', error);
      throw error;
    }
  }

  /**
   * Get user chat stats
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Chat statistics
   */
  static async getUserChatStats(userId) {
    try {
      const totalChats = await ChatThread.count({
        where: { userId, isGuest: false }
      });

      const totalMessages = await ChatMessage.count({
        include: [{
          model: Chat,
          as: 'chat',
          where: { userId, isGuest: false },
          attributes: []
        }]
      });

      return {
        totalChats,
        totalMessages,
        averageMessagesPerChat: totalChats > 0 ? Math.round(totalMessages / totalChats) : 0
      };
    } catch (error) {
      logger.error('Error getting user chat stats:', error);
      throw error;
    }
  }

  /**
   * Search user chats
   * @param {string} userId - User ID
   * @param {string} searchTerm - Search term
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} Matching chats
   */
  static async searchUserChats(userId, searchTerm, limit = 20, offset = 0) {
    try {
      const { Op } = await import('sequelize');

      return await ChatThread.findAll({
        where: {
          userId,
          isGuest: false,
          title: {
            [Op.like]: `%${searchTerm}%`
          }
        },
        order: [['updatedAt', 'DESC']],
        limit,
        offset,
      });
    } catch (error) {
      logger.error('Error searching user chats:', error);
      throw error;
    }
  }

  /**
   * Process attachment from URL for regeneration
   * @param {string} attachmentUrl - Attachment URL in format /files/{fileId}
   * @returns {Promise<Object>} Processed file data
   */
  static async processAttachmentFromUrl(attachmentUrl) {
    try {
      // Extract fileId from URL (remove /files/ prefix)
      const fileId = attachmentUrl.replace(/^\/files\//, '');

      if (!fileId) {
        throw new Error('Invalid attachment URL format');
      }

      // Get file data using the same logic as FileController
      let fileMapping = null;
      let chatMessage = null;

      // First, try to get file from secure mapping
      if (S3Service.isAvailable()) {
        fileMapping = S3Service.getSecureFileMapping(fileId);
      }

      // If not in memory mapping, look up in database
      if (!fileMapping) {
        chatMessage = await ChatMessage.findOne({
          where: { attachmentSecureId: fileId }
        });

        if (!chatMessage) {
          throw new Error('File not found or access denied');
        }
      }

      let fileBuffer;
      let fileName;
      let mimeType;

      // Get file data from S3 or local storage
      if (fileMapping || (chatMessage && chatMessage.attachmentStorageType === 's3')) {
        if (!S3Service.isAvailable()) {
          throw new Error('File storage service is not available');
        }

        const s3Key = fileMapping ? fileMapping.s3Key : chatMessage.attachmentS3Key;
        const originalName = fileMapping ? fileMapping.originalName : chatMessage.attachmentName;
        const fileMimeType = fileMapping ? fileMapping.mimeType : chatMessage.attachmentType;

        // Get file from S3
        fileBuffer = await S3Service.getFileBuffer(s3Key);
        fileName = originalName;
        mimeType = fileMimeType;
      } else if (chatMessage && chatMessage.attachmentStorageType === 'local') {
        // Get file from local storage
        const fs = await import('fs/promises');
        const path = await import('path');
        const filePath = path.join(process.cwd(), 'uploads', 'chatAttachments', chatMessage.attachmentPath);

        fileBuffer = await fs.readFile(filePath);
        fileName = chatMessage.attachmentName;
        mimeType = chatMessage.attachmentType;
      } else {
        throw new Error('File not found or invalid storage configuration');
      }

      // Create file attachment object similar to multer file
      const fileAttachment = {
        buffer: fileBuffer,
        originalname: fileName,
        mimetype: mimeType,
        size: fileBuffer.length
      };

      // Process the file using FileProcessingService
      const processedFile = await FileProcessingService.processFile(fileAttachment);

      logger.info(`Processed attachment from URL: ${attachmentUrl} (${fileName})`);
      return processedFile;

    } catch (error) {
      logger.error(`Error processing attachment from URL ${attachmentUrl}:`, error);
      throw new Error(`Failed to process attachment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
