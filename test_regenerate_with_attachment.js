/**
 * Test script for the enhanced /api/chat/regenerate endpoint with attachment support
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

// Test data
const testData = {
  // This would be a real message ID from your database
  messageId: '123e4567-e89b-12d3-a456-426614174000',
  llmModel: 'gpt-4o-mini',
  attachmentUrl: '/files/1751132443596_nqmkd6_WhatsApp_Image_2025-06-26_at_4_15_16_PM.jpeg'
};

async function testRegenerateWithAttachment() {
  try {
    console.log('Testing /api/chat/regenerate with attachment...');
    
    const response = await fetch(`${BASE_URL}/chat/regenerate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers if needed
        // 'Authorization': 'Bearer your-token-here'
      },
      body: JSON.stringify(testData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.headers.get('content-type')?.includes('text/event-stream')) {
      console.log('Streaming response detected');
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              console.log('Stream event:', data);
            } catch (e) {
              console.log('Raw stream data:', line);
            }
          }
        }
      }
    } else {
      const text = await response.text();
      console.log('Response body:', text);
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

async function testValidation() {
  console.log('\nTesting validation...');
  
  // Test with invalid attachment URL
  const invalidData = {
    messageId: '123e4567-e89b-12d3-a456-426614174000',
    attachmentUrl: 'invalid-url'
  };

  try {
    const response = await fetch(`${BASE_URL}/chat/regenerate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidData)
    });

    console.log('Validation test status:', response.status);
    const text = await response.text();
    console.log('Validation response:', text);
  } catch (error) {
    console.error('Validation test failed:', error);
  }
}

// Run tests
async function runTests() {
  console.log('Starting regenerate with attachment tests...\n');
  
  await testValidation();
  // await testRegenerateWithAttachment(); // Uncomment when you have a real messageId
  
  console.log('\nTests completed!');
}

runTests();
